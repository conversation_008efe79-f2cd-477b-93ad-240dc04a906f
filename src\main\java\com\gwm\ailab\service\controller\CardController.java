package com.gwm.ailab.service.controller;

import com.gwm.ailab.service.common.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 卡片控制器
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/cards")
@RequiredArgsConstructor
@Validated
@Tag(name = "卡片管理", description = "卡片相关的API接口")
public class CardController {


    @Operation(summary = "健康检查", description = "服务健康检查接口")
    @GetMapping("/health")
    public ResponseResult<String> health() {
        return ResponseResult.success("服务运行正常", "OK");
    }
}

# 生产环境配置
spring:
  # Redis配置
  data:
    redis:
      host: r-2zei7l72cf6lz7a580.redis.rds.aliyuncs.com
      port: 6379
      password: D7cBckzklCfyZQqpN
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 32
          max-wait: -1ms
          max-idle: 16
          min-idle: 4

# 服务器配置
server:
  port: 8080

# 日志配置
logging:
  level:
    com.gwm.ailab.service: WARN
    org.springframework.web: WARN
  file:
    name: logs/generative-card-service-prd.log

# 自定义配置
app:
  config:
    environment: prd
    debug: false
    external-api:
      base-url: http://api.example.com
      timeout: 30000
      retry-count: 3

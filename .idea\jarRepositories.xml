<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="apache-snapshots" />
      <option name="name" value="apache-snapshots" />
      <option name="url" value="http://nexus.bigdata-gw-sit.beantechyun.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="aliyun-maven" />
      <option name="name" value="aliyun-maven" />
      <option name="url" value="http://nexus.bigdata-gw-sit.beantechyun.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-releases" />
      <option name="name" value="nexus-releases" />
      <option name="url" value="http://nexus.bigdata-gw-sit.beantechyun.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-snapshots" />
      <option name="name" value="nexus-snapshots" />
      <option name="url" value="http://nexus.bigdata-gw-sit.beantechyun.com/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="cloudera" />
      <option name="name" value="cloudera" />
      <option name="url" value="https://repository.cloudera.com/artifactory/cloudera-repos" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring" />
      <option name="name" value="spring" />
      <option name="url" value="https://maven.aliyun.com/repository/spring" />
    </remote-repository>
  </component>
</project>
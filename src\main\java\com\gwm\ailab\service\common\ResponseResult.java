package com.gwm.ailab.service.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 统一响应结果类
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult<T> {
    
    /**
     * 响应码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(200, "Success", data, LocalDateTime.now());
    }

    /**
     * 成功响应（无数据）
     */
    public static ResponseResult<Void> success() {
        return new ResponseResult<>(200, "Success", null, LocalDateTime.now());
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(200, message, data, LocalDateTime.now());
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(int code, String message) {
        return new ResponseResult<>(code, message, null, LocalDateTime.now());
    }

    /**
     * 失败响应（默认500错误码）
     */
    public static ResponseResult<Void> error(String message) {
        return new ResponseResult<>(500, message, null, LocalDateTime.now());
    }

    /**
     * 参数错误响应
     */
    public static ResponseResult<Void> badRequest(String message) {
        return new ResponseResult<>(400, message, null, LocalDateTime.now());
    }

    /**
     * 未授权响应
     */
    public static ResponseResult<Void> unauthorized(String message) {
        return new ResponseResult<>(401, message, null, LocalDateTime.now());
    }

    /**
     * 禁止访问响应
     */
    public static ResponseResult<Void> forbidden(String message) {
        return new ResponseResult<>(403, message, null, LocalDateTime.now());
    }

    /**
     * 资源不存在响应
     */
    public static ResponseResult<Void> notFound(String message) {
        return new ResponseResult<>(404, message, null, LocalDateTime.now());
    }
}

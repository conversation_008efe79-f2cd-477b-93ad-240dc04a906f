package com.gwm.ailab.service.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 卡片请求DTO
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CardRequest {

    /**
     * 卡片标题
     */
    @NotBlank(message = "卡片标题不能为空")
    @Size(max = 100, message = "卡片标题长度不能超过100个字符")
    private String title;

    /**
     * 卡片内容
     */
    @NotBlank(message = "卡片内容不能为空")
    @Size(max = 1000, message = "卡片内容长度不能超过1000个字符")
    private String content;

    /**
     * 卡片类型
     */
    @NotNull(message = "卡片类型不能为空")
    private CardType type;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 卡片类型枚举
     */
    public enum CardType {
        TEXT("文本卡片"),
        IMAGE("图片卡片"),
        VIDEO("视频卡片"),
        AUDIO("音频卡片");

        private final String description;

        CardType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}

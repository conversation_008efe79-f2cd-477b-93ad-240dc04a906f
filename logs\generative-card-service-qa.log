2025-08-26 11:35:20 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Starting GenerativeCardServiceApplication using Java 17.0.12 with PID 36068 (D:\dev-center-dataservice\generative-card-service\target\classes started by GW00295473 in D:\dev-center-dataservice\generative-card-service)
2025-08-26 11:35:20 [main] DEBUG c.g.a.s.GenerativeCardServiceApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-08-26 11:35:20 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - The following 1 profile is active: "qa"
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-26 11:35:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-08-26 11:35:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-08-26 11:35:24 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 11:35:24 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-08-26 11:35:24 [main] INFO  o.a.c.c.C.[.[.[/generative-card] - Initializing Spring embedded WebApplicationContext
2025-08-26 11:35:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4165 ms
2025-08-26 11:35:24 [main] DEBUG o.s.w.f.ServerHttpObservationFilter - Filter 'webMvcObservationFilter' configured for use
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 7 mappings in 'requestMappingHandlerMapping'
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /swagger-ui*/*swagger-initializer.js, /swagger-ui*/**] in 'resourceHandlerMapping'
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-08-26 11:35:26 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-08-26 11:35:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoint(s) beneath base path '/actuator'
2025-08-26 11:35:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/generative-card'
2025-08-26 11:35:28 [main] INFO  c.g.a.s.GenerativeCardServiceApplication - Started GenerativeCardServiceApplication in 8.935 seconds (process running for 11.712)

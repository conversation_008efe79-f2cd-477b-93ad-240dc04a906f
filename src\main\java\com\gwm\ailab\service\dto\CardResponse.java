package com.gwm.ailab.service.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 卡片响应DTO
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CardResponse {

    /**
     * 卡片ID
     */
    private String id;

    /**
     * 卡片标题
     */
    private String title;

    /**
     * 卡片内容
     */
    private String content;

    /**
     * 卡片类型
     */
    private CardRequest.CardType type;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    private CardStatus status;

    /**
     * 卡片状态枚举
     */
    public enum CardStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        DELETED("已删除");

        private final String description;

        CardStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}

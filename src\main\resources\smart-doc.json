{"serverUrl": "http://localhost:8080", "pathPrefix": "/api/v1", "isStrict": false, "allInOne": true, "outPath": "target/smart-doc", "coverOld": true, "createDebugPage": true, "packageFilters": "", "md5EncryptedHtmlName": false, "style": "xt256", "projectName": "generative-card-service", "skipTransientField": true, "sortByTitle": false, "showAuthor": true, "requestFieldToUnderline": true, "responseFieldToUnderline": true, "inlineEnum": true, "recursionLimit": 7, "allInOneDocFileName": "index.html", "requestExample": "true", "responseExample": "true", "requestIgnoreParams": ["org.springframework.ui.ModelMap"], "dataDictionaries": [], "errorCodeDictionaries": [], "revisionLogs": [], "customResponseFields": [], "customRequestFields": [], "apiObjectReplacements": [], "apiConstants": [], "responseBodyAdvice": {"className": "com.gwm.ailab.service.common.ResponseResult"}, "requestBodyAdvice": {}, "groups": [{"name": "测试分组", "apis": "com.gwm.ailab.service.controller"}]}
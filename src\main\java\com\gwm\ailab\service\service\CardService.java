package com.gwm.ailab.service.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 卡片服务类
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardService {

    private final HttpService httpService;


    /**
     * 示例：调用外部API
     */
    public String callExternalApi(String endpoint) {
        try {
            return httpService.get(endpoint);
        } catch (Exception e) {
            log.error("Failed to call external API: {}", e.getMessage(), e);
            throw new RuntimeException("外部API调用失败: " + e.getMessage());
        }
    }
}

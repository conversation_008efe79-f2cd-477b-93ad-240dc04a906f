package com.gwm.ailab.service.aspect;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * API日志切面
 * 记录接口调用信息：入参、出参、接口链接、调用方地址等
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Aspect
@Component
public class ApiLogAspect {

    /**
     * 定义切点：拦截所有Controller层的方法
     */
    @Pointcut("execution(* com.gwm.ailab.service.controller..*.*(..))")
    public void apiPointcut() {
    }

    /**
     * 环绕通知：记录接口调用的完整信息
     */
    @Around("apiPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
        
        // 构建请求日志信息
        ApiLogInfo logInfo = buildRequestLogInfo(joinPoint, request);
        
        // 记录请求开始日志
        log.info("=== API Request Start ===");
        log.info("Request Info: {}", JSON.toJSONString(logInfo));
        
        Object result = null;
        Exception exception = null;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            // 记录响应日志
            logResponse(logInfo, result, exception, executionTime);
        }
    }

    /**
     * 构建请求日志信息
     */
    private ApiLogInfo buildRequestLogInfo(JoinPoint joinPoint, HttpServletRequest request) {
        ApiLogInfo logInfo = new ApiLogInfo();
        
        if (request != null) {
            // 基本请求信息
            logInfo.setMethod(request.getMethod());
            logInfo.setUrl(request.getRequestURL().toString());
            logInfo.setUri(request.getRequestURI());
            logInfo.setQueryString(request.getQueryString());
            
            // 客户端信息
            logInfo.setRemoteAddr(getClientIpAddress(request));
            logInfo.setUserAgent(request.getHeader("User-Agent"));
            logInfo.setReferer(request.getHeader("Referer"));
            
            // 请求头信息
            logInfo.setHeaders(getRequestHeaders(request));
        }
        
        // 方法信息
        logInfo.setClassName(joinPoint.getTarget().getClass().getSimpleName());
        logInfo.setMethodName(joinPoint.getSignature().getName());
        
        // 请求参数
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            logInfo.setArgs(Arrays.toString(args));
            logInfo.setArgsDetail(JSON.toJSONString(args));
        }
        
        return logInfo;
    }

    /**
     * 记录响应日志
     */
    private void logResponse(ApiLogInfo logInfo, Object result, Exception exception, long executionTime) {
        logInfo.setExecutionTime(executionTime);
        
        if (exception != null) {
            logInfo.setSuccess(false);
            logInfo.setErrorMessage(exception.getMessage());
            logInfo.setErrorClass(exception.getClass().getSimpleName());
            log.error("=== API Request Failed ===");
            log.error("Response Info: {}", JSON.toJSONString(logInfo));
            log.error("Exception Details: ", exception);
        } else {
            logInfo.setSuccess(true);
            if (result != null) {
                logInfo.setResult(JSON.toJSONString(result));
            }
            log.info("=== API Request Success ===");
            log.info("Response Info: {}", JSON.toJSONString(logInfo));
        }
        
        log.info("=== API Request End ({}ms) ===", executionTime);
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，第一个IP为客户端真实IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取请求头信息
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        
        return headers;
    }

    /**
     * API日志信息实体类
     */
    public static class ApiLogInfo {
        private String method;
        private String url;
        private String uri;
        private String queryString;
        private String remoteAddr;
        private String userAgent;
        private String referer;
        private Map<String, String> headers;
        private String className;
        private String methodName;
        private String args;
        private String argsDetail;
        private String result;
        private boolean success;
        private String errorMessage;
        private String errorClass;
        private long executionTime;

        // Getters and Setters
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getUri() { return uri; }
        public void setUri(String uri) { this.uri = uri; }
        public String getQueryString() { return queryString; }
        public void setQueryString(String queryString) { this.queryString = queryString; }
        public String getRemoteAddr() { return remoteAddr; }
        public void setRemoteAddr(String remoteAddr) { this.remoteAddr = remoteAddr; }
        public String getUserAgent() { return userAgent; }
        public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
        public String getReferer() { return referer; }
        public void setReferer(String referer) { this.referer = referer; }
        public Map<String, String> getHeaders() { return headers; }
        public void setHeaders(Map<String, String> headers) { this.headers = headers; }
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        public String getMethodName() { return methodName; }
        public void setMethodName(String methodName) { this.methodName = methodName; }
        public String getArgs() { return args; }
        public void setArgs(String args) { this.args = args; }
        public String getArgsDetail() { return argsDetail; }
        public void setArgsDetail(String argsDetail) { this.argsDetail = argsDetail; }
        public String getResult() { return result; }
        public void setResult(String result) { this.result = result; }
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public String getErrorClass() { return errorClass; }
        public void setErrorClass(String errorClass) { this.errorClass = errorClass; }
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
    }
}

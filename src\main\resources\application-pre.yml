# SIT环境配置
spring:
  # Redis配置
  data:
    redis:
      host: r-2ze6q0wyapxmnvzcwl.redis.rds.aliyuncs.com
      port: 6379
      password: YeVtHMquCtsq5zA2JWjS
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 16
          max-wait: -1ms
          max-idle: 8
          min-idle: 2

# 服务器配置
server:
  port: 8080

# 日志配置
logging:
  level:
    com.gwm.ailab.service: INFO
    org.springframework.web: INFO
  file:
    name: logs/generative-card-service-sit.log

# 自定义配置
app:
  config:
    environment: sit
    debug: false
    external-api:
      base-url: http://sit-api.example.com
      timeout: 30000
      retry-count: 3

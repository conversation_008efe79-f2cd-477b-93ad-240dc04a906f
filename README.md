# Generative Card Service

基于Spring Boot 3.2 + Java 17的生成式卡片服务

## 项目特性

- **Java 17**: 使用最新的Java 17语法特性
- **Spring Boot 3.2**: 最新的Spring Boot框架
- **SpringDoc OpenAPI**: 自动生成API文档
- **FastJSON 2**: 高性能JSON处理
- **Lombok**: 简化Java代码
- **Smart Doc**: 智能文档生成
- **Redis**: 缓存和数据存储
- **OkHttp**: HTTP客户端
- **AOP**: 全局接口日志记录

## 项目结构

```
src/
├── main/
│   ├── java/com/gwm/ailab/service/
│   │   ├── GenerativeCardServiceApplication.java  # 主启动类
│   │   ├── aspect/
│   │   │   └── ApiLogAspect.java                  # AOP日志切面
│   │   ├── common/
│   │   │   └── ResponseResult.java                # 统一响应结果
│   │   ├── config/
│   │   │   ├── OkHttpConfig.java                  # OkHttp配置
│   │   │   └── RedisConfig.java                   # Redis配置
│   │   ├── controller/
│   │   │   └── CardController.java                # 卡片控制器
│   │   ├── dto/
│   │   │   ├── CardRequest.java                   # 请求DTO
│   │   │   └── CardResponse.java                  # 响应DTO
│   │   └── service/
│   │       ├── CardService.java                   # 卡片服务
│   │       └── HttpService.java                   # HTTP服务
│   └── resources/
│       ├── application.yml                        # 主配置文件
│       ├── application-qa.yml                     # QA环境配置
│       ├── application-sit.yml                    # SIT环境配置
│       ├── application-prd.yml                    # 生产环境配置
│       └── smart-doc.json                         # Smart Doc配置
└── test/
    └── java/com/gwm/ailab/service/
        └── GenerativeCardServiceApplicationTests.java
```

## 环境配置

### QA环境
```bash
java -jar generative-card-service.jar --spring.profiles.active=qa
```

### SIT环境
```bash
java -jar generative-card-service.jar --spring.profiles.active=sit
```

### 生产环境
```bash
java -jar generative-card-service.jar --spring.profiles.active=prd
```

## API文档

启动应用后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8080/api/v1/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api/v1/api-docs

## 主要功能

### 1. 卡片管理
- 创建卡片
- 获取卡片详情
- 获取用户卡片列表
- 更新卡片
- 删除卡片

### 2. Redis集成
- 卡片数据缓存
- 用户卡片列表管理
- 连接测试接口

### 3. HTTP客户端
- 支持GET、POST、PUT、DELETE请求
- 异步请求支持
- 自动重试机制
- 请求日志记录

### 4. AOP日志记录
自动记录所有API接口的：
- 请求方法和URL
- 请求参数
- 响应结果
- 执行时间
- 客户端IP地址
- User-Agent等信息

## 快速开始

### 1. 环境要求
- Java 17+
- Maven 3.6+
- Redis 6.0+

### 2. 构建项目
```bash
mvn clean compile
```

### 3. 运行测试
```bash
mvn test
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 生成文档
```bash
mvn smart-doc:html
```

## 配置说明

### Redis配置
在对应环境的配置文件中修改Redis连接信息：
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
```

### 外部API配置
```yaml
app:
  config:
    external-api:
      base-url: http://api.example.com
      timeout: 30000
      retry-count: 3
```

## 健康检查

- **应用健康检查**: GET /api/v1/cards/health
- **Redis连接测试**: GET /api/v1/cards/test/redis
- **Spring Actuator**: GET /api/v1/actuator/health

